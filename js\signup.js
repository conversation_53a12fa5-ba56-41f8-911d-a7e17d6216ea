function getUsers() {
    const users = localStorage.getItem('users');
    return users ? JSON.parse(users) : [];
}

function saveUsers(users) {
    localStorage.setItem('users', JSON.stringify(users));
}

function emailExists(email) {
    const users = getUsers();
    return users.some(user => user.email.toLowerCase() === email.toLowerCase());
}

function showAlert(message, type) {
    const alertDiv = document.getElementById('alertMessage');
    alertDiv.className = `alert alert-${type} mt-3`;
    alertDiv.textContent = message;
    alertDiv.style.display = 'block';

    setTimeout(() => {
        alertDiv.style.display = 'none';
    }, 3000);
}

function validateForm(name, email, password) {
    if (name.trim().length < 2) {
        showAlert('Name must be at least 2 characters long', 'danger');
        return false;
    }

    if (!email.includes('@') || !email.includes('.')) {
        showAlert('Please enter a valid email address', 'danger');
        return false;
    }

    if (password.length < 6) {
        showAlert('Password must be at least 6 characters long', 'danger');
        return false;
    }

    return true;
}

function checkIfLoggedIn() {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        window.location.href = 'welcome.html';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    checkIfLoggedIn();
});

document.getElementById('signupForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const name = document.getElementById('signupName').value;
    const email = document.getElementById('signupEmail').value;
    const password = document.getElementById('signupPassword').value;

    if (!validateForm(name, email, password)) {
        return;
    }

    if (emailExists(email)) {
        showAlert('Email already exists! Please use a different email.', 'danger');
        return;
    }

    const newUser = {
        id: Date.now(),
        name: name.trim(),
        email: email.toLowerCase().trim(),
        password: password
    };

    const users = getUsers();
    users.push(newUser);
    saveUsers(users);

    showAlert('Account created successfully! Redirecting to login...', 'success');

    document.getElementById('signupForm').reset();

    setTimeout(() => {
        window.location.href = 'index.html';
    }, 2000);
});


