// Get users from localStorage
function getUsers() {
    const users = localStorage.getItem('users');
    return users ? JSON.parse(users) : [];
}

// Save users to localStorage
function saveUsers(users) {
    localStorage.setItem('users', JSON.stringify(users));
}

// Check if email already exists
function emailExists(email) {
    const users = getUsers();
    return users.some(user => user.email.toLowerCase() === email.toLowerCase());
}

// Show alert message
function showAlert(message, type) {
    const alertDiv = document.getElementById('alertMessage');
    alertDiv.className = `alert alert-${type} mt-3`;
    alertDiv.textContent = message;
    alertDiv.style.display = 'block';
    
    // Hide alert after 3 seconds
    setTimeout(() => {
        alertDiv.style.display = 'none';
    }, 3000);
}

// Validate form inputs
function validateForm(name, email, password) {
    if (name.trim().length < 2) {
        showAlert('Name must be at least 2 characters long', 'danger');
        return false;
    }
    
    if (!email.includes('@') || !email.includes('.')) {
        showAlert('Please enter a valid email address', 'danger');
        return false;
    }
    
    if (password.length < 6) {
        showAlert('Password must be at least 6 characters long', 'danger');
        return false;
    }
    
    return true;
}

// Check if user is already logged in
function checkIfLoggedIn() {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        // Redirect to welcome page if already logged in
        window.location.href = 'welcome.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Check if already logged in
    checkIfLoggedIn();
});

// Handle form submission
document.getElementById('signupForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const name = document.getElementById('signupName').value;
    const email = document.getElementById('signupEmail').value;
    const password = document.getElementById('signupPassword').value;
    
    // Validate inputs
    if (!validateForm(name, email, password)) {
        return;
    }
    
    // Check if email already exists
    if (emailExists(email)) {
        showAlert('Email already exists! Please use a different email.', 'danger');
        return;
    }
    
    // Create new user object
    const newUser = {
        id: Date.now(), // Simple ID generation
        name: name.trim(),
        email: email.toLowerCase().trim(),
        password: password
    };
    
    // Get existing users and add new user
    const users = getUsers();
    users.push(newUser);
    saveUsers(users);
    
    // Show success message
    showAlert('Account created successfully! Redirecting to login...', 'success');
    
    // Clear form
    document.getElementById('signupForm').reset();
    
    // Redirect to login page after 2 seconds
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 2000);
});

// Add some styling for alerts
const style = document.createElement('style');
style.textContent = `
    .alert-success {
        background-color: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
    }
    .alert-danger {
        background-color: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        color: #dc3545;
    }
`;
document.head.appendChild(style);
