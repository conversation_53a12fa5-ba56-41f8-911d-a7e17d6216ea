.input{
    background-color: #24353F;
    color: #17A2B8;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.7);
    margin-top: 50px;
}

.input .form-control{
    background-color: #24353F ;
    color: #17A2B8;
    border: 1px solid white;
}

.input .form-control:focus{
    background-color: #24353F ;
    color: #17A2B8;
    border: 1px solid #17A2B8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.input .form-control::placeholder{
    color: white;
}

.input .form-control:focus::placeholder{
    color: white;
}

.btn{
    background-color: #24353F;
    color: #17A2B8;
    border: 2px solid #17A2B8;
}

.btn:hover{
    background-color: #17A2B8;
    color: #24353F;
    border: 2px solid #24353F;
    transition: 0.5s;
}

/* Success message styling */
.success-message {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid #28a745;
    color: #28a745;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    text-align: center;
    font-weight: 500;
}


.error-message {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid #dc3545;
    color: #dc3545;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    text-align: center;
    font-weight: 500;
}

