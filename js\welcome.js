// Get current user from localStorage
function getCurrentUser() {
    const user = localStorage.getItem('currentUser');
    return user ? JSON.parse(user) : null;
}

// Clear user session
function clearUserSession() {
    localStorage.removeItem('currentUser');
}

// Check if user is logged in
function checkAuth() {
    const user = getCurrentUser();
    if (!user) {
        // Redirect to login if no user session
        window.location.href = 'index.html';
        return null;
    }
    return user;
}

// Display user information
function displayUserInfo(user) {
    document.getElementById('userName').textContent = user.name;
    document.getElementById('displayName').textContent = user.name;
    document.getElementById('displayEmail').textContent = user.email;
}

// Handle logout
function handleLogout() {
    clearUserSession();
    
    // Show logout message (optional)
    alert('You have been logged out successfully!');
    
    // Redirect to login page
    window.location.href = 'index.html';
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    const user = checkAuth();
    
    if (user) {
        // Display user information
        displayUserInfo(user);
        
        // Add logout event listener
        document.getElementById('logoutBtn').addEventListener('click', handleLogout);
    }
});

// Prevent back button after logout
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        // Check if user is still logged in
        const user = getCurrentUser();
        if (!user) {
            window.location.href = 'index.html';
        }
    }
});
