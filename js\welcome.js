function getCurrentUser() {
    const user = localStorage.getItem('currentUser');
    return user ? JSON.parse(user) : null;
}

function clearUserSession() {
    localStorage.removeItem('currentUser');
}

function checkAuth() {
    const user = getCurrentUser();
    if (!user) {
        window.location.href = 'index.html';
        return null;
    }
    return user;
}

function displayUserInfo(user) {
    document.getElementById('userName').textContent = user.name;
    document.getElementById('displayName').textContent = user.name;
    document.getElementById('displayEmail').textContent = user.email;
}

function handleLogout() {
    clearUserSession();
    alert('You have been logged out successfully!');
    window.location.href = 'index.html';
}

document.addEventListener('DOMContentLoaded', function() {
    const user = checkAuth();

    if (user) {
        displayUserInfo(user);
        document.getElementById('logoutBtn').addEventListener('click', handleLogout);
    }
});

window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        const user = getCurrentUser();
        if (!user) {
            window.location.href = 'index.html';
        }
    }
});
