
function getUsers() {
    const users = localStorage.getItem('users');
    return users ? JSON.parse(users) : [];
}


function findUser(email, password) {
    const users = getUsers();
    return users.find(user =>
        user.email.toLowerCase() === email.toLowerCase() &&
        user.password === password
    );
}


function saveCurrentUser(user) {
    localStorage.setItem('currentUser', JSON.stringify(user));
}


function showAlert(message, type) {
    
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} mt-3`;
    alertDiv.textContent = message;

    
    const form = document.querySelector('form');
    form.appendChild(alertDiv);

    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}


function checkIfLoggedIn() {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        
        window.location.href = 'welcome.html';
    }
}


document.addEventListener('DOMContentLoaded', function() {
    
    checkIfLoggedIn();

    const form = document.querySelector('form');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const email = document.getElementById('Email1').value;
        const password = document.getElementById('Password').value;

        
        if (!email || !password) {
            showAlert('Please fill in all fields', 'danger');
            return;
        }

        
        const user = findUser(email, password);

        if (user) {
            
            saveCurrentUser(user);

            
            showAlert('Login successful! Redirecting...', 'success');

            
            setTimeout(() => {
                window.location.href = 'welcome.html';
            }, 1500);
        } else {
            
            showAlert('Invalid email or password. Please try again.', 'danger');
        }
    });
});


const style = document.createElement('style');
style.textContent = `
    .alert-success {
        background-color: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
    }
    .alert-danger {
        background-color: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        color: #dc3545;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
    }
`;
document.head.appendChild(style);