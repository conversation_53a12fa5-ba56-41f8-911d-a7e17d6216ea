// Get users from localStorage
function getUsers() {
    const users = localStorage.getItem('users');
    return users ? JSON.parse(users) : [];
}

// Find user by email and password
function findUser(email, password) {
    const users = getUsers();
    return users.find(user =>
        user.email.toLowerCase() === email.toLowerCase() &&
        user.password === password
    );
}

// Save current user session
function saveCurrentUser(user) {
    localStorage.setItem('currentUser', JSON.stringify(user));
}

// Show alert message
function showAlert(message, type) {
    // Remove existing alert if any
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} mt-3`;
    alertDiv.textContent = message;

    // Add to form
    const form = document.querySelector('form');
    form.appendChild(alertDiv);

    // Hide alert after 3 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// Check if user is already logged in
function checkIfLoggedIn() {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        // Redirect to welcome page if already logged in
        window.location.href = 'welcome.html';
    }
}

// Handle form submission
document.addEventListener('DOMContentLoaded', function() {
    // Check if already logged in
    checkIfLoggedIn();

    const form = document.querySelector('form');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const email = document.getElementById('Email1').value;
        const password = document.getElementById('Password').value;

        // Validate inputs
        if (!email || !password) {
            showAlert('Please fill in all fields', 'danger');
            return;
        }

        // Find user
        const user = findUser(email, password);

        if (user) {
            // Save user session
            saveCurrentUser(user);

            // Show success message
            showAlert('Login successful! Redirecting...', 'success');

            // Redirect to welcome page
            setTimeout(() => {
                window.location.href = 'welcome.html';
            }, 1500);
        } else {
            // Show error message
            showAlert('Invalid email or password. Please try again.', 'danger');
        }
    });
});

// Add some styling for alerts
const style = document.createElement('style');
style.textContent = `
    .alert-success {
        background-color: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
    }
    .alert-danger {
        background-color: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        color: #dc3545;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
    }
`;
document.head.appendChild(style);